#!/usr/bin/env python
# -*- coding: utf-8 -*-

print("=" * 50)
print("快速测试 FancyImpute 安装")
print("=" * 50)

# 测试基础导入
try:
    import pandas as pd
    import numpy as np
    print("✓ pandas 和 numpy 导入成功")
except ImportError as e:
    print(f"❌ 基础包导入失败: {e}")
    exit(1)

# 测试 fancyimpute 导入
try:
    import fancyimpute
    print("✓ fancyimpute 包导入成功")
    print(f"  版本: {fancyimpute.__version__ if hasattr(fancyimpute, '__version__') else '未知'}")
except ImportError as e:
    print(f"❌ fancyimpute 导入失败: {e}")
    exit(1)

# 测试 SoftImpute 导入
try:
    from fancyimpute import SoftImpute
    print("✓ SoftImpute 类导入成功")
except ImportError as e:
    print(f"❌ SoftImpute 导入失败: {e}")
    exit(1)

# 测试创建 SoftImpute 实例
try:
    imputer = SoftImpute(max_iters=5, verbose=False)
    print("✓ SoftImpute 实例创建成功")
except Exception as e:
    print(f"❌ SoftImpute 实例创建失败: {e}")
    exit(1)

# 测试简单的插补
try:
    # 创建测试数据
    np.random.seed(42)
    test_data = np.random.randn(10, 5)
    test_data[0, 0] = np.nan
    test_data[1, 2] = np.nan
    
    print("✓ 测试数据创建成功")
    print(f"  数据形状: {test_data.shape}")
    print(f"  缺失值数量: {np.isnan(test_data).sum()}")
    
    # 执行插补
    result = imputer.fit_transform(test_data)
    print("✓ 插补执行成功")
    print(f"  结果形状: {result.shape}")
    print(f"  结果中的缺失值: {np.isnan(result).sum()}")
    
except Exception as e:
    print(f"❌ 插补测试失败: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n" + "=" * 50)
print("🎉 所有测试通过！FancyImpute 可以正常使用")
print("=" * 50)

# 测试读取数据文件
try:
    data_path = "../data/breast.csv"
    data = pd.read_csv(data_path)
    print(f"✓ 数据文件读取成功: {data.shape}")
except Exception as e:
    print(f"⚠ 数据文件读取失败: {e}")

print("\n现在可以运行主程序: python softimpute_fancyimpute.py")
