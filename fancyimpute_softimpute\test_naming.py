#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试新的文件命名格式
"""

import os

def test_naming_format():
    """测试文件命名格式"""
    print("=" * 50)
    print("测试文件命名格式")
    print("=" * 50)
    
    # 测试不同的数据文件路径
    test_paths = [
        '../data/news.csv',
        '../data/breast.csv', 
        '../data/spam.csv',
        '../data/myocard.csv'
    ]
    
    missing_rates = [0.1, 0.2, 0.3, 0.4, 0.5]
    
    print("预期的文件命名格式:")
    print("-" * 50)
    
    for data_file_path in test_paths:
        # 提取数据集名称
        dataset_name = os.path.splitext(os.path.basename(data_file_path))[0].capitalize()
        
        print(f"\n数据文件: {data_file_path}")
        print(f"数据集名称: {dataset_name}")
        print("生成的文件名:")
        
        for missing_rate in missing_rates:
            output_filename = f"SoftImpute-{dataset_name}-{missing_rate:.1f}.csv"
            print(f"  缺失率 {missing_rate:.1f}: {output_filename}")
    
    print(f"\n" + "=" * 50)
    print("命名格式验证完成！")
    print("格式: SoftImpute-数据集名称-缺失率.csv")
    print("=" * 50)

if __name__ == "__main__":
    test_naming_format()
