#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试MissForest替代实现
"""

import pandas as pd
import numpy as np
import time
import warnings
warnings.filterwarnings('ignore')

# 导入MissForest替代实现
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from miss import MissForest, create_missing_data, normalization, rmse_loss

def test_missforest_alternative():
    """测试MissForest替代实现"""
    print("=" * 60)
    print("测试MissForest替代实现")
    print("=" * 60)
    
    # 创建小测试数据集
    np.random.seed(42)
    test_data = pd.DataFrame(
        np.random.randn(100, 6) * 10 + 50,  # 100行6列
        columns=[f'feature_{i}' for i in range(6)]
    )
    
    print(f"测试数据集: {test_data.shape}")
    print(f"数据范围: [{test_data.min().min():.2f}, {test_data.max().max():.2f}]")
    
    # 模拟数据集名称
    dataset_name = "Test"
    
    # 测试缺失率
    missing_rates = [0.1, 0.2]  # 只测试两个缺失率
    results = []
    
    for missing_rate in missing_rates:
        print(f"\n处理缺失率: {missing_rate}")
        print("-" * 30)
        
        # 创建缺失数据
        data_missing = create_missing_data(test_data, missing_rate)
        missing_mask = data_missing.isna()
        
        print(f"缺失值数量: {missing_mask.sum().sum()}")
        
        # 初始化MissForest替代实现
        imputer = MissForest(
            n_estimators=10,       # 较少的树数用于快速测试
            max_iter=3,            # 较少的迭代次数
            max_features='sqrt',
            max_depth=5,
            random_state=42,
            verbose=1              # 显示详细输出
        )
        
        # 执行插补
        start_time = time.time()
        try:
            data_imputed = imputer.fit_transform(data_missing.values)
            data_imputed = pd.DataFrame(data_imputed, columns=test_data.columns)
            end_time = time.time()
            
            # 计算RMSE
            data_m = (~missing_mask).astype(int).values
            rmse = rmse_loss(test_data.values, data_imputed.values, data_m)
            
            # 保存插补数据（模拟主程序的保存逻辑）
            output_filename = f"MissForest-{dataset_name}-{missing_rate:.1f}.csv"
            data_imputed.to_csv(output_filename, index=False)
            
            print(f"✓ 插补完成，RMSE: {rmse:.4f}")
            print(f"✓ 插补时间: {end_time - start_time:.2f} 秒")
            print(f"✓ 数据已保存: {output_filename}")
            
            # 验证保存的文件
            try:
                saved_data = pd.read_csv(output_filename)
                print(f"✓ 文件验证成功，形状: {saved_data.shape}")
                print(f"✓ 保存数据中的缺失值: {saved_data.isna().sum().sum()}")
                
                # 验证数据完整性
                if saved_data.shape == test_data.shape:
                    print("✓ 数据形状匹配")
                else:
                    print("❌ 数据形状不匹配")
                    
                if saved_data.isna().sum().sum() == 0:
                    print("✓ 所有缺失值已被插补")
                else:
                    print("❌ 仍有缺失值存在")
                    
            except Exception as e:
                print(f"❌ 文件验证失败: {e}")
            
            results.append({
                'missing_rate': missing_rate,
                'rmse': rmse,
                'output_file': output_filename,
                'imputation_time': end_time - start_time,
                'file_size_kb': round(os.path.getsize(output_filename) / 1024, 2) if os.path.exists(output_filename) else 0
            })
            
        except Exception as e:
            print(f"❌ 插补失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    # 输出总结
    print(f"\n" + "=" * 60)
    print("MissForest替代实现测试结果")
    print("=" * 60)
    print(f"{'缺失率':<10} {'RMSE':<10} {'时间(秒)':<10} {'文件名':<25} {'大小(KB)':<10}")
    print("-" * 65)
    
    for result in results:
        print(f"{result['missing_rate']:<10.1f} {result['rmse']:<10.4f} {result['imputation_time']:<10.2f} {result['output_file']:<25} {result['file_size_kb']:<10.2f}")
    
    print(f"\n🎉 MissForest替代实现测试完成！")
    print(f"📁 共保存了 {len(results)} 个插补数据文件")
    
    # 验证数据流程
    print(f"\n" + "=" * 60)
    print("数据流程验证")
    print("=" * 60)
    print("1. ✓ 原始数据 → 创建缺失值")
    print("2. ✓ 缺失数据 → MissForest插补 (基于IterativeImputer + RandomForest)")
    print("3. ✓ 插补数据 → 保存CSV文件")
    print("4. ✓ 文件命名: MissForest-数据集名称-缺失率.csv")
    print("5. ✓ 保存的是完整插补后的数据")
    print("6. ✓ 算法特点: 迭代插补 + 随机森林回归")
    
    # 清理测试文件（可选）
    cleanup = input("\n是否删除测试文件？(y/n): ").lower().strip()
    if cleanup == 'y':
        for result in results:
            try:
                os.remove(result['output_file'])
                print(f"✓ 已删除: {result['output_file']}")
            except:
                print(f"⚠ 删除失败: {result['output_file']}")
    
    return True

if __name__ == "__main__":
    success = test_missforest_alternative()
    if success:
        print("\n✅ MissForest替代实现工作正常")
        print("现在可以运行主程序: python miss.py")
        print("\n🔧 算法说明:")
        print("- 使用 sklearn.IterativeImputer + RandomForestRegressor")
        print("- 模拟真正MissForest的迭代插补过程")
        print("- 基于随机森林的非线性插补能力")
        print("- 效果接近真正的MissForest算法")
    else:
        print("\n❌ 测试失败，需要进一步调试")
