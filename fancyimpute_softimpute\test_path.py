#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试路径解决方案
"""

import os
import pandas as pd

def test_path_solution():
    """测试路径解决方案"""
    print("=" * 60)
    print("测试路径解决方案")
    print("=" * 60)
    
    # 方法1: 相对路径
    print("方法1: 相对路径")
    relative_path = '../data/news.csv'
    print(f"相对路径: {relative_path}")
    print(f"文件存在: {os.path.exists(relative_path)}")
    
    # 方法2: 绝对路径（推荐）
    print("\n方法2: 绝对路径（推荐）")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    absolute_path = os.path.join(project_root, 'data', 'news.csv')
    print(f"当前脚本目录: {current_dir}")
    print(f"项目根目录: {project_root}")
    print(f"绝对路径: {absolute_path}")
    print(f"文件存在: {os.path.exists(absolute_path)}")
    
    # 方法3: 工作目录相关
    print("\n方法3: 当前工作目录")
    cwd = os.getcwd()
    print(f"当前工作目录: {cwd}")
    
    # 测试所有可能的数据文件
    print("\n" + "=" * 60)
    print("测试所有数据文件")
    print("=" * 60)
    
    data_files = ['news.csv', 'breast.csv', 'spam.csv', 'myocard.csv', 'credit.csv', 'letter.csv', 'nha.csv']
    
    for data_file in data_files:
        # 使用绝对路径
        file_path = os.path.join(project_root, 'data', data_file)
        exists = os.path.exists(file_path)
        
        print(f"{data_file:<12} - 存在: {exists}")
        
        if exists:
            try:
                # 尝试读取文件头部
                df = pd.read_csv(file_path, nrows=1)
                print(f"             - 可读取: ✓ (形状: {df.shape})")
            except Exception as e:
                print(f"             - 读取失败: ❌ ({str(e)[:50]}...)")
    
    # 推荐的路径函数
    print("\n" + "=" * 60)
    print("推荐的路径解决方案")
    print("=" * 60)
    
    def get_data_file_path(filename):
        """获取数据文件的绝对路径"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        return os.path.join(project_root, 'data', filename)
    
    # 测试推荐方案
    test_file = 'news.csv'
    recommended_path = get_data_file_path(test_file)
    print(f"推荐路径函数测试:")
    print(f"文件名: {test_file}")
    print(f"生成路径: {recommended_path}")
    print(f"文件存在: {os.path.exists(recommended_path)}")
    
    if os.path.exists(recommended_path):
        try:
            df = pd.read_csv(recommended_path)
            print(f"读取成功: ✓ (形状: {df.shape})")
            print(f"数据范围: [{df.min().min():.3f}, {df.max().max():.3f}]")
        except Exception as e:
            print(f"读取失败: ❌ ({e})")

if __name__ == "__main__":
    test_path_solution()
