# SoftImpute 矩阵插补方法

## 方法简介

SoftImpute是一种基于矩阵分解的插补方法，通过低秩矩阵近似来填补缺失值。该方法特别适用于大规模稀疏矩阵的插补任务。

## 核心原理

1. **矩阵分解**: 将原始矩阵分解为低秩矩阵的乘积
2. **软阈值**: 使用软阈值操作来控制矩阵的秩
3. **迭代优化**: 通过迭代过程逐步改善插补结果
4. **收敛判断**: 基于收敛阈值判断算法是否收敛

## 算法特点

### 优势
- ✅ **适用于大规模数据**: 能够处理高维稀疏矩阵
- ✅ **捕获潜在结构**: 通过低秩近似发现数据的内在模式
- ✅ **理论基础扎实**: 基于矩阵完成理论
- ✅ **内存效率高**: 相比完整矩阵方法更节省内存

### 局限性
- ❌ **假设低秩结构**: 要求数据具有低秩特性
- ❌ **参数敏感**: 对收缩参数和秩的选择较为敏感
- ❌ **计算复杂**: 涉及奇异值分解，计算量较大
- ❌ **初始化影响**: 初始化方法会影响最终结果

## 参数配置

当前实现使用的参数配置（快速但效果较差）：

```python
SoftImpute(
    shrinkage_value=None,           # 自动选择收缩参数
    convergence_threshold=1e-2,     # 较宽松的收敛条件
    max_iters=50,                   # 较少的最大迭代次数
    max_rank=min(10, min(data.shape) // 2),  # 限制矩阵秩
    n_power_iterations=1,           # 较少的幂迭代次数
    init_fill_method="zero",        # 零初始化
    min_value=None,                 # 不限制最小值
    max_value=None,                 # 不限制最大值
    normalizer=None,                # 不使用额外标准化
    verbose=False                   # 不显示详细输出
)
```

## 使用方法

### 安装依赖
```bash
pip install fancyimpute
```

### 运行插补
```bash
cd softimpute
python softimpute_imputation.py
```

## 实验设置

- **数据集**: spam.csv (垃圾邮件数据集)
- **缺失率**: [0.1, 0.2, 0.3, 0.4, 0.5]
- **评估指标**: 标准化RMSE
- **随机种子**: 42 (确保可重复性)

## 输出结果

程序会生成以下输出：
1. **控制台输出**: 实时显示插补进度和结果
2. **CSV文件**: `softimpute_spam_results.csv` 包含详细结果
3. **性能统计**: 平均RMSE、时间统计等

## 结果文件格式

CSV文件包含以下列：
- `missing_rate`: 缺失率
- `rmse`: 标准化RMSE值
- `imputation_time`: 插补时间（秒）
- `missing_count`: 缺失值数量
- `total_elements`: 总数据点数量

## 与其他方法的比较

| 方法 | 基本原理 | 适用场景 | 计算复杂度 |
|------|----------|----------|------------|
| KNN | 近邻相似性 | 中小规模数据 | O(n²) |
| MICE | 迭代回归 | 混合类型数据 | O(n·p·k) |
| MissForest | 随机森林 | 非线性关系 | O(n·log(n)·p) |
| **SoftImpute** | **矩阵分解** | **大规模稀疏矩阵** | **O(n·p·r)** |

其中：n=样本数，p=特征数，k=迭代次数，r=矩阵秩

## 改进建议

为了获得更好的插补效果，可以考虑以下参数调整：

1. **增加迭代次数**: `max_iters=200`
2. **更严格收敛**: `convergence_threshold=1e-4`
3. **更好初始化**: `init_fill_method="mean"`
4. **增加矩阵秩**: `max_rank=min(50, min(data.shape) // 2)`
5. **更多幂迭代**: `n_power_iterations=3`

## 注意事项

1. **内存使用**: 大数据集可能需要大量内存
2. **收敛性**: 某些数据可能难以收敛
3. **数据预处理**: 建议对数据进行适当的标准化
4. **参数调优**: 需要根据具体数据集调整参数

## 参考文献

- Mazumder, R., Hastie, T., & Tibshirani, R. (2010). Spectral regularization algorithms for learning large incomplete matrices. Journal of Machine Learning Research, 11, 2287-2322.
- Hastie, T., Mazumder, R., Lee, J. D., & Zadeh, R. (2015). Matrix completion and low-rank SVD via fast alternating least squares. Journal of Machine Learning Research, 16, 3367-3402.
