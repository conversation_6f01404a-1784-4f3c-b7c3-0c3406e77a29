import pandas as pd
import numpy as np
import time
import warnings
warnings.filterwarnings('ignore')

# 高质量的MissForest替代实现
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.base import BaseEstimator, TransformerMixin

class MissForest(BaseEstimator, TransformerMixin):
    """
    高质量的MissForest替代实现
    基于IterativeImputer + RandomForest，模拟真正的MissForest算法
    """

    def __init__(self, n_estimators=100, max_iter=10, criterion='mse',
                 max_features='sqrt', max_depth=None, n_jobs=1,
                 random_state=None, verbose=0):
        """
        初始化MissForest替代实现

        参数:
        - n_estimators: 随机森林中树的数量
        - max_iter: 最大迭代次数
        - criterion: 分裂准则
        - max_features: 每棵树使用的特征数
        - max_depth: 树的最大深度
        - n_jobs: 并行作业数
        - random_state: 随机种子
        - verbose: 详细输出级别
        """
        self.n_estimators = n_estimators
        self.max_iter = max_iter
        self.criterion = criterion
        self.max_features = max_features
        self.max_depth = max_depth
        self.n_jobs = n_jobs
        self.random_state = random_state
        self.verbose = verbose

        # 创建基于RandomForest的IterativeImputer
        self.imputer = IterativeImputer(
            estimator=RandomForestRegressor(
                n_estimators=self.n_estimators,
                criterion=self.criterion if self.criterion in ['mse', 'mae'] else 'squared_error',
                max_features=self.max_features,
                max_depth=self.max_depth,
                random_state=self.random_state,
                n_jobs=self.n_jobs
            ),
            max_iter=self.max_iter,
            random_state=self.random_state,
            verbose=self.verbose
        )

        if self.verbose > 0:
            print(f"MissForest替代实现初始化完成:")
            print(f"  - 树数量: {self.n_estimators}")
            print(f"  - 最大迭代: {self.max_iter}")
            print(f"  - 特征选择: {self.max_features}")
            print(f"  - 最大深度: {self.max_depth}")

    def fit(self, X, y=None):
        """训练插补器"""
        self.imputer.fit(X)
        return self

    def transform(self, X):
        """执行插补"""
        if self.verbose > 0:
            print("执行MissForest插补...")
        return self.imputer.transform(X)

    def fit_transform(self, X, y=None):
        """训练并执行插补"""
        if self.verbose > 0:
            print("执行MissForest插补 (基于RandomForest的迭代插补)...")
            missing_count = np.isnan(X).sum()
            print(f"  - 输入缺失值数量: {missing_count}")

        result = self.imputer.fit_transform(X)

        if self.verbose > 0:
            result_missing = np.isnan(result).sum()
            print(f"  - 输出缺失值数量: {result_missing}")
            print("  - MissForest插补完成")

        return result

print("✓ 成功加载MissForest替代实现 (基于sklearn.IterativeImputer + RandomForestRegressor)")

def create_missing_data(data, missing_rate):
    """
    随机创建缺失数据
    """
    data_missing = data.copy()
    n_rows, n_cols = data.shape
    n_missing = int(n_rows * n_cols * missing_rate)

    # 随机选择位置创建缺失值
    missing_indices = np.random.choice(n_rows * n_cols, n_missing, replace=False)
    row_indices = missing_indices // n_cols
    col_indices = missing_indices % n_cols

    for i, j in zip(row_indices, col_indices):
        data_missing.iloc[i, j] = np.nan

    return data_missing

def normalization(data, parameters=None):
    """
    将数据标准化到 [0,1] 范围
    """
    if parameters is None:
        # 计算标准化参数
        min_val = np.nanmin(data, axis=0)
        max_val = np.nanmax(data, axis=0)
        parameters = {'min_val': min_val, 'max_val': max_val}
    else:
        min_val = parameters['min_val']
        max_val = parameters['max_val']

    # 避免除零错误
    range_val = max_val - min_val
    range_val[range_val == 0] = 1

    # 标准化
    normalized_data = (data - min_val) / range_val

    return normalized_data, parameters

def rmse_loss(ori_data, imputed_data, data_m):
    """
    计算标准化后的RMSE
    ori_data: 原始数据
    imputed_data: 插补数据
    data_m: 缺失掩码 (1表示观测值，0表示缺失值)
    """
    # 重新标准化两个数据集
    ori_data_norm, norm_parameters = normalization(ori_data)
    imputed_data_norm, _ = normalization(imputed_data, norm_parameters)

    # 在标准化后的数据上计算RMSE (只计算缺失位置)
    nominator = np.sum(((1-data_m) * ori_data_norm - (1-data_m) * imputed_data_norm)**2)
    denominator = np.sum(1-data_m)

    if denominator == 0:
        return np.inf

    rmse = np.sqrt(nominator/float(denominator))
    return rmse

def main():
    # 读取数据，将空值填充为0
    print("读取数据...")

    # 构建稳健的文件路径
    import os
    data_file_path = 'data/myocard.csv'
    data = pd.read_csv(data_file_path).fillna(0)

    # 提取数据集名称（从文件路径中获取，去掉扩展名）
    dataset_name = os.path.splitext(os.path.basename(data_file_path))[0].capitalize()
    print(f"数据集: {dataset_name}数据集")
    print(f"数据形状: {data.shape}")
    print(f"特征数量: {data.shape[1]}")
    print(f"样本数量: {data.shape[0]}")
    print(f"数据类型: {data.dtypes.value_counts().to_dict()}")
    print(f"数据范围: [{data.min().min():.3f}, {data.max().max():.3f}]")
    print(f"总数据点: {data.shape[0] * data.shape[1]:,}")
    print(f"原始数据中的空值已填充为0")

    # 缺失率列表
    missing_rates = [0.1, 0.2, 0.3, 0.4, 0.5]

    # 存储结果
    results = []

    # 设置随机种子以确保可重复性
    np.random.seed(42)

    print("\n开始插补实验...")
    print("=" * 60)

    for missing_rate in missing_rates:
        print(f"\n处理缺失率: {missing_rate}")
        print("-" * 40)

        # 创建缺失数据
        data_missing = create_missing_data(data, missing_rate)
        missing_mask = data_missing.isna()

        print(f"创建的缺失值数量: {missing_mask.sum().sum()}")
        print(f"实际缺失率: {missing_mask.sum().sum() / (data.shape[0] * data.shape[1]):.3f}")

        # 初始化MissForest - 使用改进的参数配置
        imputer = MissForest(
            n_estimators=50,       # 增加树数量，提高性能
            max_iter=10,           # 增加迭代次数，确保收敛
            criterion='mse',       # 回归损失函数
            max_features='sqrt',   # 使用sqrt(n_features)个特征
            max_depth=10,          # 增加树深度，提高学习能力
            n_jobs=1,              # 不使用并行（避免兼容性问题）
            random_state=42,       # 固定随机种子，保证可复现
            verbose=1              # 显示插补进度
        )

        # 记录插补时间
        start_time = time.time()

        try:
            # 执行插补
            data_imputed = imputer.fit_transform(data_missing)

            # 转换为DataFrame
            data_imputed = pd.DataFrame(data_imputed, columns=data.columns)

            end_time = time.time()
            imputation_time = end_time - start_time

            # 计算标准化RMSE
            # 创建缺失掩码 (1表示观测值，0表示缺失值)
            data_m = (~missing_mask).astype(int).values
            rmse = rmse_loss(data.values, data_imputed.values, data_m)

            # 保存插补完成的数据为CSV文件
            # 文件名格式: 插补方法-数据集名称-缺失率
            output_filename = f"MissForest-{dataset_name}-{missing_rate:.1f}.csv"
            data_imputed.to_csv(output_filename, index=False)
            print(f"插补数据已保存: {output_filename}")

            # 存储结果
            result = {
                'missing_rate': missing_rate,
                'rmse': rmse,
                'imputation_time': imputation_time,
                'missing_count': missing_mask.sum().sum(),
                'total_elements': data.shape[0] * data.shape[1],
                'output_file': output_filename
            }
            results.append(result)

            print(f"插补完成!")
            print(f"RMSE: {rmse:.4f}")
            print(f"插补时间: {imputation_time:.2f} 秒")

        except Exception as e:
            print(f"插补失败: {str(e)}")
            result = {
                'missing_rate': missing_rate,
                'rmse': np.inf,
                'imputation_time': np.inf,
                'missing_count': missing_mask.sum().sum(),
                'total_elements': data.shape[0] * data.shape[1],
                'error': str(e)
            }
            results.append(result)

    # 输出汇总结果
    print("\n" + "=" * 60)
    print("MissForest插补结果汇总")
    print("=" * 60)
    print(f"{'缺失率':<10} {'RMSE':<12} {'插补时间(秒)':<15} {'缺失值数量':<12} {'保存文件':<25}")
    print("-" * 85)

    for result in results:
        if 'error' not in result:
            output_file = result.get('output_file', 'N/A')
            print(f"{result['missing_rate']:<10.1f} {result['rmse']:<12.4f} {result['imputation_time']:<15.2f} {result['missing_count']:<12} {output_file:<25}")
        else:
            print(f"{result['missing_rate']:<10.1f} {'ERROR':<12} {'ERROR':<15} {result['missing_count']:<12} {'ERROR':<25}")

    # 保存结果到CSV
    results_df = pd.DataFrame(results)
    results_df.to_csv('missforest_results.csv', index=False)
    print(f"\n结果已保存到 'missforest_results.csv'")

    # 输出保存的插补数据文件总结
    print(f"\n📁 插补数据文件保存总结:")
    print("-" * 50)
    valid_results = [r for r in results if 'error' not in r and 'output_file' in r]
    for result in valid_results:
        print(f"✓ 缺失率 {result['missing_rate']:.1f}: {result['output_file']}")
    print(f"\n总共保存了 {len(valid_results)} 个插补数据文件")

    return results

if __name__ == "__main__":
    results = main()