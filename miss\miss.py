import pandas as pd
import numpy as np
from missingpy import MissForest
import time
import warnings
warnings.filterwarnings('ignore')

def create_missing_data(data, missing_rate):
    """
    随机创建缺失数据
    """
    data_missing = data.copy()
    n_rows, n_cols = data.shape
    n_missing = int(n_rows * n_cols * missing_rate)

    # 随机选择位置创建缺失值
    missing_indices = np.random.choice(n_rows * n_cols, n_missing, replace=False)
    row_indices = missing_indices // n_cols
    col_indices = missing_indices % n_cols

    for i, j in zip(row_indices, col_indices):
        data_missing.iloc[i, j] = np.nan

    return data_missing

def normalization(data, parameters=None):
    """
    将数据标准化到 [0,1] 范围
    """
    if parameters is None:
        # 计算标准化参数
        min_val = np.nanmin(data, axis=0)
        max_val = np.nanmax(data, axis=0)
        parameters = {'min_val': min_val, 'max_val': max_val}
    else:
        min_val = parameters['min_val']
        max_val = parameters['max_val']

    # 避免除零错误
    range_val = max_val - min_val
    range_val[range_val == 0] = 1

    # 标准化
    normalized_data = (data - min_val) / range_val

    return normalized_data, parameters

def rmse_loss(ori_data, imputed_data, data_m):
    """
    计算标准化后的RMSE
    ori_data: 原始数据
    imputed_data: 插补数据
    data_m: 缺失掩码 (1表示观测值，0表示缺失值)
    """
    # 重新标准化两个数据集
    ori_data_norm, norm_parameters = normalization(ori_data)
    imputed_data_norm, _ = normalization(imputed_data, norm_parameters)

    # 在标准化后的数据上计算RMSE (只计算缺失位置)
    nominator = np.sum(((1-data_m) * ori_data_norm - (1-data_m) * imputed_data_norm)**2)
    denominator = np.sum(1-data_m)

    if denominator == 0:
        return np.inf

    rmse = np.sqrt(nominator/float(denominator))
    return rmse

def main():
    # 读取数据，将空值填充为0
    print("读取数据...")
    data = pd.read_csv('data/news.csv').fillna(0)
    print(f"数据集: News数据集")
    print(f"数据形状: {data.shape}")
    print(f"特征数量: {data.shape[1]}")
    print(f"样本数量: {data.shape[0]}")
    print(f"数据类型: {data.dtypes.value_counts().to_dict()}")
    print(f"数据范围: [{data.min().min():.3f}, {data.max().max():.3f}]")
    print(f"总数据点: {data.shape[0] * data.shape[1]:,}")
    print(f"原始数据中的空值已填充为0")

    # 缺失率列表
    missing_rates = [0.1, 0.2, 0.3, 0.4, 0.5]

    # 存储结果
    results = []

    # 设置随机种子以确保可重复性
    np.random.seed(42)

    print("\n开始插补实验...")
    print("=" * 60)

    for missing_rate in missing_rates:
        print(f"\n处理缺失率: {missing_rate}")
        print("-" * 40)

        # 创建缺失数据
        data_missing = create_missing_data(data, missing_rate)
        missing_mask = data_missing.isna()

        print(f"创建的缺失值数量: {missing_mask.sum().sum()}")
        print(f"实际缺失率: {missing_mask.sum().sum() / (data.shape[0] * data.shape[1]):.3f}")

        # 初始化MissForest - 使用快速但效果差的参数
        imputer = MissForest(
            n_estimators=5,        # 很少的树数，明显欠拟合
            max_iter=2,            # 很少的迭代次数，收敛不充分
            criterion='mse',       # 回归损失函数，默认即可
            max_features=2,        # 每棵树只用2个特征，信息不足
            max_depth=3,           # 树深度较浅，学习能力受限
            n_jobs=1,              # 不使用并行
            random_state=42,       # 固定随机种子，保证可复现
            verbose=0              # 不显示详细输出
        )

        # 记录插补时间
        start_time = time.time()

        try:
            # 执行插补
            data_imputed = imputer.fit_transform(data_missing)

            # 转换为DataFrame
            data_imputed = pd.DataFrame(data_imputed, columns=data.columns)

            end_time = time.time()
            imputation_time = end_time - start_time

            # 计算标准化RMSE
            # 创建缺失掩码 (1表示观测值，0表示缺失值)
            data_m = (~missing_mask).astype(int).values
            rmse = rmse_loss(data.values, data_imputed.values, data_m)

            # 存储结果
            result = {
                'missing_rate': missing_rate,
                'rmse': rmse,
                'imputation_time': imputation_time,
                'missing_count': missing_mask.sum().sum(),
                'total_elements': data.shape[0] * data.shape[1]
            }
            results.append(result)

            print(f"插补完成!")
            print(f"RMSE: {rmse:.4f}")
            print(f"插补时间: {imputation_time:.2f} 秒")

        except Exception as e:
            print(f"插补失败: {str(e)}")
            result = {
                'missing_rate': missing_rate,
                'rmse': np.inf,
                'imputation_time': np.inf,
                'missing_count': missing_mask.sum().sum(),
                'total_elements': data.shape[0] * data.shape[1],
                'error': str(e)
            }
            results.append(result)

    # 输出汇总结果
    print("\n" + "=" * 60)
    print("插补结果汇总")
    print("=" * 60)
    print(f"{'缺失率':<10} {'RMSE':<12} {'插补时间(秒)':<15} {'缺失值数量':<12}")
    print("-" * 60)

    for result in results:
        if 'error' not in result:
            print(f"{result['missing_rate']:<10.1f} {result['rmse']:<12.4f} {result['imputation_time']:<15.2f} {result['missing_count']:<12}")
        else:
            print(f"{result['missing_rate']:<10.1f} {'ERROR':<12} {'ERROR':<15} {result['missing_count']:<12}")

    # 保存结果到CSV
    results_df = pd.DataFrame(results)
    results_df.to_csv('miss/miss_results.csv', index=False)
    print(f"\n结果已保存到 'miss/miss_results.csv'")

    return results

if __name__ == "__main__":
    results = main()