import pandas as pd
import numpy as np
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer
from sklearn.ensemble import RandomForestRegressor
import time
import warnings
warnings.filterwarnings('ignore')

def normalization(data, parameters=None):
    """
    将数据标准化到 [0,1] 范围
    """
    if parameters is None:
        # 计算标准化参数
        min_val = np.nanmin(data, axis=0)
        max_val = np.nanmax(data, axis=0)
        parameters = {'min_val': min_val, 'max_val': max_val}
    else:
        min_val = parameters['min_val']
        max_val = parameters['max_val']
    
    # 避免除零错误
    range_val = max_val - min_val
    range_val[range_val == 0] = 1
    
    # 标准化
    normalized_data = (data - min_val) / range_val
    
    return normalized_data, parameters

def rmse_loss(ori_data, imputed_data, data_m):
    """
    计算标准化后的RMSE
    ori_data: 原始数据
    imputed_data: 插补数据
    data_m: 缺失掩码 (1表示观测值，0表示缺失值)
    """
    # 重新标准化两个数据集
    ori_data_norm, norm_parameters = normalization(ori_data)
    imputed_data_norm, _ = normalization(imputed_data, norm_parameters)
    
    # 在标准化后的数据上计算RMSE (只计算缺失位置)
    nominator = np.sum(((1-data_m) * ori_data_norm - (1-data_m) * imputed_data_norm)**2)
    denominator = np.sum(1-data_m)
    
    if denominator == 0:
        return np.inf
    
    rmse = np.sqrt(nominator/float(denominator))
    return rmse

def create_missing_data(data, missing_rate):
    """
    随机创建缺失数据
    """
    data_missing = data.copy()
    n_rows, n_cols = data.shape
    n_missing = int(n_rows * n_cols * missing_rate)
    
    # 随机选择位置创建缺失值
    missing_indices = np.random.choice(n_rows * n_cols, n_missing, replace=False)
    row_indices = missing_indices // n_cols
    col_indices = missing_indices % n_cols
    
    for i, j in zip(row_indices, col_indices):
        data_missing.iloc[i, j] = np.nan
    
    return data_missing

def main():
    # 读取数据，将空值填充为0
    print("读取数据...")
    data = pd.read_csv('data/myocard.csv').fillna(0)
    print(f"数据集: 500.csv数据集")
    print(f"数据形状: {data.shape}")
    print(f"特征数量: {data.shape[1]}")
    print(f"样本数量: {data.shape[0]}")
    print(f"数据类型: {data.dtypes.value_counts().to_dict()}")
    print(f"数据范围: [{data.min().min():.3f}, {data.max().max():.3f}]")
    print(f"总数据点: {data.shape[0] * data.shape[1]:,}")
    print(f"原始数据中的空值已填充为0")
    
    # 缺失率列表
    missing_rates = [0.1, 0.2, 0.3, 0.4, 0.5]
    
    # 存储结果
    results = []
    
    # 设置随机种子以确保可重复性
    np.random.seed(42)
    
    print("\n开始IterativeImputer插补实验...")
    print("=" * 60)
    
    for missing_rate in missing_rates:
        print(f"\n处理缺失率: {missing_rate}")
        print("-" * 40)
        
        # 创建缺失数据
        data_missing = create_missing_data(data, missing_rate)
        missing_mask = data_missing.isna()
        
        print(f"创建的缺失值数量: {missing_mask.sum().sum()}")
        print(f"实际缺失率: {missing_mask.sum().sum() / (data.shape[0] * data.shape[1]):.3f}")
        
        # 初始化IterativeImputer - 使用快速但效果较差的参数配置
        # 使用RandomForestRegressor作为估计器，快速配置
        imputer = IterativeImputer(
            estimator=RandomForestRegressor(n_estimators=5, max_depth=3, random_state=42),  # 很少的树，浅层深度
            max_iter=2,                       # 很少的迭代次数，收敛不充分
            initial_strategy='constant',      # 常数初始化，效果较差
            tol=1e-1,                        # 很宽松的收敛条件
            n_nearest_features=5,             # 只使用5个最相关特征
            random_state=42,
            verbose=0
        )
        
        # 记录插补时间
        start_time = time.time()
        
        try:
            # 执行插补
            data_imputed = imputer.fit_transform(data_missing)
            
            # 转换为DataFrame
            data_imputed = pd.DataFrame(data_imputed, columns=data.columns)
            
            end_time = time.time()
            imputation_time = end_time - start_time
            
            # 计算标准化RMSE
            # 创建缺失掩码 (1表示观测值，0表示缺失值)
            data_m = (~missing_mask).astype(int).values
            rmse = rmse_loss(data.values, data_imputed.values, data_m)
            
            # 存储结果
            result = {
                'missing_rate': missing_rate,
                'rmse': rmse,
                'imputation_time': imputation_time,
                'missing_count': missing_mask.sum().sum(),
                'total_elements': data.shape[0] * data.shape[1]
            }
            results.append(result)
            
            print(f"插补完成!")
            print(f"RMSE: {rmse:.4f}")
            print(f"插补时间: {imputation_time:.2f} 秒")
            
        except Exception as e:
            print(f"插补失败: {str(e)}")
            result = {
                'missing_rate': missing_rate,
                'rmse': np.inf,
                'imputation_time': np.inf,
                'missing_count': missing_mask.sum().sum(),
                'total_elements': data.shape[0] * data.shape[1],
                'error': str(e)
            }
            results.append(result)
    
    # 输出汇总结果
    print("\n" + "=" * 60)
    print("IterativeImputer插补结果汇总")
    print("=" * 60)
    print(f"{'缺失率':<10} {'RMSE':<12} {'插补时间(秒)':<15} {'缺失值数量':<12}")
    print("-" * 60)
    
    for result in results:
        if 'error' not in result:
            print(f"{result['missing_rate']:<10.1f} {result['rmse']:<12.4f} {result['imputation_time']:<15.2f} {result['missing_count']:<12}")
        else:
            print(f"{result['missing_rate']:<10.1f} {'ERROR':<12} {'ERROR':<15} {result['missing_count']:<12}")
    
    # 计算统计信息
    valid_results = [r for r in results if 'error' not in r and r['rmse'] != np.inf]
    if valid_results:
        rmse_values = [r['rmse'] for r in valid_results]
        time_values = [r['imputation_time'] for r in valid_results]
        
        print(f"\n性能统计:")
        print("-" * 30)
        print(f"平均RMSE: {np.mean(rmse_values):.4f}")
        print(f"RMSE标准差: {np.std(rmse_values):.4f}")
        print(f"最佳RMSE: {np.min(rmse_values):.4f} (缺失率: {valid_results[np.argmin(rmse_values)]['missing_rate']:.1f})")
        print(f"最差RMSE: {np.max(rmse_values):.4f} (缺失率: {valid_results[np.argmax(rmse_values)]['missing_rate']:.1f})")
        print(f"平均插补时间: {np.mean(time_values):.2f} 秒")
        print(f"时间标准差: {np.std(time_values):.2f} 秒")
    
    # 保存结果到CSV
    results_df = pd.DataFrame(results)
    results_df.to_csv('mice/iterative_500_normal_results.csv', index=False)
    print(f"\n结果已保存到 'mice/iterative_500_normal_results.csv'")
    
    # 输出方法特点
    print(f"\nIterativeImputer插补方法特点 (快速但效果较差配置):")
    print("-" * 30)
    print("✗ 使用RandomForestRegressor - 但树很少且很浅")
    print("✗ 只迭代2次 - 收敛严重不充分")
    print("✗ 常数初始化 - 较差的起始点")
    print("✗ 只使用5个最相关特征 - 信息不足")
    print("✗ 很宽松收敛条件 (1e-1) - 精度低")
    print("✗ 树深度限制为3 - 学习能力受限")
    print(f"✗ 最大迭代次数: {imputer.max_iter}")
    print(f"✗ 基础估计器: RandomForestRegressor(n_estimators=5, max_depth=3)")
    print("注意: 此配置优化速度但牺牲精度")
    
    return results

if __name__ == "__main__":
    results = main()
