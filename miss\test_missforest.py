#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试MissForest插补功能
"""

import pandas as pd
import numpy as np
import time
import warnings
warnings.filterwarnings('ignore')

# 导入修改后的MissForest
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from miss import MissForest, create_missing_data, normalization, rmse_loss

def test_missforest():
    """测试MissForest插补功能"""
    print("=" * 60)
    print("测试MissForest插补功能")
    print("=" * 60)
    
    # 创建小测试数据集
    np.random.seed(42)
    test_data = pd.DataFrame(
        np.random.randn(50, 4) * 10 + 50,  # 50行4列
        columns=[f'feature_{i}' for i in range(4)]
    )
    
    print(f"测试数据集: {test_data.shape}")
    print(f"数据范围: [{test_data.min().min():.2f}, {test_data.max().max():.2f}]")
    
    # 测试缺失率
    missing_rate = 0.2
    
    print(f"\n处理缺失率: {missing_rate}")
    print("-" * 30)
    
    # 创建缺失数据
    data_missing = create_missing_data(test_data, missing_rate)
    missing_mask = data_missing.isna()
    
    print(f"缺失值数量: {missing_mask.sum().sum()}")
    
    # 初始化MissForest
    imputer = MissForest(
        n_estimators=5,
        max_iter=3,
        max_features='sqrt',
        max_depth=3,
        random_state=42,
        verbose=0
    )
    
    # 执行插补
    start_time = time.time()
    try:
        data_imputed = imputer.fit_transform(data_missing.values)
        data_imputed = pd.DataFrame(data_imputed, columns=test_data.columns)
        end_time = time.time()
        
        # 计算RMSE
        data_m = (~missing_mask).astype(int).values
        rmse = rmse_loss(test_data.values, data_imputed.values, data_m)
        
        print(f"✓ 插补完成，RMSE: {rmse:.4f}")
        print(f"✓ 插补时间: {end_time - start_time:.2f} 秒")
        print(f"✓ 插补后缺失值数量: {data_imputed.isna().sum().sum()}")
        
        # 验证数据完整性
        if data_imputed.shape == test_data.shape:
            print("✓ 数据形状匹配")
        else:
            print("❌ 数据形状不匹配")
            
        if data_imputed.isna().sum().sum() == 0:
            print("✓ 所有缺失值已被插补")
        else:
            print("❌ 仍有缺失值存在")
            
    except Exception as e:
        print(f"❌ 插补失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print(f"\n🎉 MissForest插补功能测试成功！")
    return True

if __name__ == "__main__":
    success = test_missforest()
    if success:
        print("\n✅ MissForest替代实现工作正常")
        print("现在可以运行主程序: python miss.py")
    else:
        print("\n❌ 测试失败，需要进一步调试")
