#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试保存插补数据功能的脚本
使用小数据集快速验证新功能
"""

import pandas as pd
import numpy as np
import time
import warnings
warnings.filterwarnings('ignore')

# 导入主程序的函数
from softimpute_fancyimpute import normalization, rmse_loss, create_missing_data

# 尝试导入fancyimpute
try:
    from fancyimpute import SoftImpute
    FANCYIMPUTE_AVAILABLE = True
    print("✓ 使用真实的 fancyimpute.SoftImpute")
except ImportError:
    print("⚠ fancyimpute 未安装，使用简单替代实现")
    FANCYIMPUTE_AVAILABLE = False
    
    class SoftImpute:
        def __init__(self, **kwargs):
            self.max_iters = kwargs.get('max_iters', 100)
            self.convergence_threshold = kwargs.get('convergence_threshold', 0.001)
        
        def fit_transform(self, X):
            """简单的均值填充替代"""
            X_imputed = X.copy()
            for col in range(X.shape[1]):
                col_data = X[:, col]
                mean_val = np.nanmean(col_data)
                if np.isnan(mean_val):
                    mean_val = 0
                X_imputed[:, col] = np.where(np.isnan(col_data), mean_val, col_data)
            return X_imputed

def test_save_feature():
    """测试保存功能"""
    print("=" * 60)
    print("测试插补数据保存功能")
    print("=" * 60)
    
    # 创建小测试数据集
    np.random.seed(42)
    test_data = pd.DataFrame(
        np.random.randn(100, 5) * 10 + 50,  # 100行5列，均值50，标准差10
        columns=[f'feature_{i}' for i in range(5)]
    )
    
    print(f"测试数据集: {test_data.shape}")
    print(f"数据范围: [{test_data.min().min():.2f}, {test_data.max().max():.2f}]")
    
    # 测试缺失率
    missing_rates = [0.1, 0.2]  # 只测试两个缺失率
    results = []
    
    for missing_rate in missing_rates:
        print(f"\n处理缺失率: {missing_rate}")
        print("-" * 30)
        
        # 创建缺失数据
        data_missing = create_missing_data(test_data, missing_rate)
        missing_mask = data_missing.isna()
        
        print(f"缺失值数量: {missing_mask.sum().sum()}")
        
        # 初始化插补器
        imputer = SoftImpute(max_iters=10, convergence_threshold=0.01)
        
        # 执行插补
        start_time = time.time()
        data_imputed = imputer.fit_transform(data_missing.values)
        data_imputed = pd.DataFrame(data_imputed, columns=test_data.columns)
        end_time = time.time()
        
        # 计算RMSE
        data_m = (~missing_mask).astype(int).values
        rmse = rmse_loss(test_data.values, data_imputed.values, data_m)
        
        # 保存插补数据
        output_filename = f"test_softimpute_imputed_missing_{missing_rate:.1f}.csv"
        data_imputed.to_csv(output_filename, index=False)
        
        print(f"✓ 插补完成，RMSE: {rmse:.4f}")
        print(f"✓ 数据已保存: {output_filename}")
        
        # 验证保存的文件
        try:
            saved_data = pd.read_csv(output_filename)
            print(f"✓ 文件验证成功，形状: {saved_data.shape}")
            print(f"✓ 保存数据中的缺失值: {saved_data.isna().sum().sum()}")
        except Exception as e:
            print(f"❌ 文件验证失败: {e}")
        
        results.append({
            'missing_rate': missing_rate,
            'rmse': rmse,
            'output_file': output_filename,
            'file_size_kb': round(pd.read_csv(output_filename).memory_usage(deep=True).sum() / 1024, 2)
        })
    
    # 输出总结
    print(f"\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"{'缺失率':<10} {'RMSE':<10} {'文件名':<35} {'大小(KB)':<10}")
    print("-" * 65)
    
    for result in results:
        print(f"{result['missing_rate']:<10.1f} {result['rmse']:<10.4f} {result['output_file']:<35} {result['file_size_kb']:<10.2f}")
    
    print(f"\n🎉 保存功能测试完成！")
    print(f"📁 共保存了 {len(results)} 个插补数据文件")
    
    # 清理测试文件（可选）
    import os
    cleanup = input("\n是否删除测试文件？(y/n): ").lower().strip()
    if cleanup == 'y':
        for result in results:
            try:
                os.remove(result['output_file'])
                print(f"✓ 已删除: {result['output_file']}")
            except:
                print(f"⚠ 删除失败: {result['output_file']}")

if __name__ == "__main__":
    test_save_feature()
