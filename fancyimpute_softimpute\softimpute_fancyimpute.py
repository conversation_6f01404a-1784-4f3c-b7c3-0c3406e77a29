import pandas as pd
import numpy as np
import time
import warnings
warnings.filterwarnings('ignore')

# 尝试导入fancyimpute，如果失败则使用替代实现
try:
    from fancyimpute import SoftImpute
    FANCYIMPUTE_AVAILABLE = True
    print("✓ 成功导入 fancyimpute.SoftImpute")
except ImportError:
    print("⚠ fancyimpute 未安装，使用替代实现")
    FANCYIMPUTE_AVAILABLE = False

    # 简单的替代实现
    class SoftImpute:
        def __init__(self, **kwargs):
            self.shrinkage_value = kwargs.get('shrinkage_value', None)
            self.convergence_threshold = kwargs.get('convergence_threshold', 0.001)
            self.max_iters = kwargs.get('max_iters', 100)
            self.max_rank = kwargs.get('max_rank', None)
            self.n_power_iterations = kwargs.get('n_power_iterations', 1)
            self.init_fill_method = kwargs.get('init_fill_method', "zero")
            self.min_value = kwargs.get('min_value', None)
            self.max_value = kwargs.get('max_value', None)
            self.normalizer = kwargs.get('normalizer', None)
            self.verbose = kwargs.get('verbose', False)
            print(f"使用替代SoftImpute实现 (max_iters={self.max_iters})")

        def fit_transform(self, X):
            """简单的替代实现 - 使用均值填充"""
            print("执行替代插补算法...")
            X_imputed = X.copy()
            for col in range(X.shape[1]):
                col_data = X[:, col]
                mean_val = np.nanmean(col_data)
                if np.isnan(mean_val):
                    mean_val = 0
                X_imputed[:, col] = np.where(np.isnan(col_data), mean_val, col_data)
            return X_imputed

def normalization(data, parameters=None):
    """
    将数据标准化到 [0,1] 范围
    """
    if parameters is None:
        # 计算标准化参数
        min_val = np.nanmin(data, axis=0)
        max_val = np.nanmax(data, axis=0)
        parameters = {'min_val': min_val, 'max_val': max_val}
    else:
        min_val = parameters['min_val']
        max_val = parameters['max_val']
    
    # 避免除零错误
    range_val = max_val - min_val
    range_val[range_val == 0] = 1
    
    # 标准化
    normalized_data = (data - min_val) / range_val
    
    return normalized_data, parameters

def rmse_loss(ori_data, imputed_data, data_m):
    """
    计算标准化后的RMSE
    ori_data: 原始数据
    imputed_data: 插补数据
    data_m: 缺失掩码 (1表示观测值，0表示缺失值)
    """
    # 重新标准化两个数据集
    ori_data_norm, norm_parameters = normalization(ori_data)
    imputed_data_norm, _ = normalization(imputed_data, norm_parameters)
    
    # 在标准化后的数据上计算RMSE (只计算缺失位置)
    nominator = np.sum(((1-data_m) * ori_data_norm - (1-data_m) * imputed_data_norm)**2)
    denominator = np.sum(1-data_m)
    
    if denominator == 0:
        return np.inf
    
    rmse = np.sqrt(nominator/float(denominator))
    return rmse

def create_missing_data(data, missing_rate):
    """
    随机创建缺失数据
    """
    data_missing = data.copy()
    n_rows, n_cols = data.shape
    n_missing = int(n_rows * n_cols * missing_rate)
    
    # 随机选择位置创建缺失值
    missing_indices = np.random.choice(n_rows * n_cols, n_missing, replace=False)
    row_indices = missing_indices // n_cols
    col_indices = missing_indices % n_cols
    
    for i, j in zip(row_indices, col_indices):
        data_missing.iloc[i, j] = np.nan
    
    return data_missing

def main():
    # 读取数据，将空值填充为0
    print("读取数据...")
    data_file_path = '../data/news.csv'
    data = pd.read_csv(data_file_path).fillna(0)

    # 提取数据集名称（从文件路径中获取，去掉扩展名）
    import os
    dataset_name = os.path.splitext(os.path.basename(data_file_path))[0].capitalize()
    print(f"数据集: {dataset_name}数据集")
    print(f"数据形状: {data.shape}")
    print(f"特征数量: {data.shape[1]}")
    print(f"样本数量: {data.shape[0]}")
    print(f"数据类型: {data.dtypes.value_counts().to_dict()}")
    print(f"数据范围: [{data.min().min():.3f}, {data.max().max():.3f}]")
    print(f"总数据点: {data.shape[0] * data.shape[1]:,}")
    print(f"原始数据中的空值已填充为0")
    
    # 缺失率列表
    missing_rates = [0.1, 0.2, 0.3, 0.4, 0.5]
    
    # 存储结果
    results = []
    
    # 设置随机种子以确保可重复性
    np.random.seed(42)
    
    if FANCYIMPUTE_AVAILABLE:
        print("\n开始FancyImpute SoftImpute插补实验...")
    else:
        print("\n开始SoftImpute插补实验 (使用替代实现)...")
    print("=" * 60)
    
    for missing_rate in missing_rates:
        print(f"\n处理缺失率: {missing_rate}")
        print("-" * 40)
        
        # 创建缺失数据
        data_missing = create_missing_data(data, missing_rate)
        missing_mask = data_missing.isna()
        
        print(f"创建的缺失值数量: {missing_mask.sum().sum()}")
        print(f"实际缺失率: {missing_mask.sum().sum() / (data.shape[0] * data.shape[1]):.3f}")
        
        # 初始化SoftImpute - 使用fancyimpute包
        # SoftImpute基于矩阵分解的低秩近似方法，适用于矩阵补全
        imputer = SoftImpute(
            shrinkage_value=None,      # 自动选择收缩参数
            convergence_threshold=0.001,  # 收敛阈值
            max_iters=100,             # 最大迭代次数
            max_rank=None,             # 最大矩阵秩，None表示自动选择
            n_power_iterations=1,      # 幂迭代次数
            init_fill_method="zero",   # 初始填充方法
            min_value=None,            # 最小值约束
            max_value=None,            # 最大值约束
            normalizer=None,           # 标准化方法
            verbose=False              # 不显示详细输出
        )
        
        # 记录插补时间
        start_time = time.time()
        
        try:
            # 执行插补 - fancyimpute的SoftImpute使用fit_transform方法
            data_imputed = imputer.fit_transform(data_missing.values)
            
            # 转换为DataFrame
            data_imputed = pd.DataFrame(data_imputed, columns=data.columns)
            
            end_time = time.time()
            imputation_time = end_time - start_time
            
            # 计算标准化RMSE
            # 创建缺失掩码 (1表示观测值，0表示缺失值)
            data_m = (~missing_mask).astype(int).values
            rmse = rmse_loss(data.values, data_imputed.values, data_m)

            # 保存插补完成的数据为CSV文件
            # 文件名格式: 插补方法-数据集名称-缺失率
            output_filename = f"SoftImpute-{dataset_name}-{missing_rate:.1f}.csv"
            data_imputed.to_csv(output_filename, index=False)
            print(f"插补数据已保存: {output_filename}")

            # 存储结果
            result = {
                'missing_rate': missing_rate,
                'rmse': rmse,
                'imputation_time': imputation_time,
                'missing_count': missing_mask.sum().sum(),
                'total_elements': data.shape[0] * data.shape[1],
                'output_file': output_filename
            }
            results.append(result)

            print(f"插补完成!")
            print(f"RMSE: {rmse:.4f}")
            print(f"插补时间: {imputation_time:.2f} 秒")
            
        except Exception as e:
            print(f"插补失败: {str(e)}")
            result = {
                'missing_rate': missing_rate,
                'rmse': np.inf,
                'imputation_time': np.inf,
                'missing_count': missing_mask.sum().sum(),
                'total_elements': data.shape[0] * data.shape[1],
                'error': str(e)
            }
            results.append(result)
    
    # 输出汇总结果
    print("\n" + "=" * 60)
    if FANCYIMPUTE_AVAILABLE:
        print("FancyImpute SoftImpute插补结果汇总")
    else:
        print("SoftImpute插补结果汇总 (替代实现)")
    print("=" * 60)
    print(f"{'缺失率':<10} {'RMSE':<12} {'插补时间(秒)':<15} {'缺失值数量':<12} {'保存文件':<30}")
    print("-" * 90)

    for result in results:
        if 'error' not in result:
            output_file = result.get('output_file', 'N/A')
            print(f"{result['missing_rate']:<10.1f} {result['rmse']:<12.4f} {result['imputation_time']:<15.2f} {result['missing_count']:<12} {output_file:<30}")
        else:
            print(f"{result['missing_rate']:<10.1f} {'ERROR':<12} {'ERROR':<15} {result['missing_count']:<12} {'ERROR':<30}")
    
    # 计算统计信息
    valid_results = [r for r in results if 'error' not in r and r['rmse'] != np.inf]
    if valid_results:
        rmse_values = [r['rmse'] for r in valid_results]
        time_values = [r['imputation_time'] for r in valid_results]
        
        print(f"\n性能统计:")
        print("-" * 30)
        print(f"平均RMSE: {np.mean(rmse_values):.4f}")
        print(f"RMSE标准差: {np.std(rmse_values):.4f}")
        print(f"最佳RMSE: {np.min(rmse_values):.4f} (缺失率: {valid_results[np.argmin(rmse_values)]['missing_rate']:.1f})")
        print(f"最差RMSE: {np.max(rmse_values):.4f} (缺失率: {valid_results[np.argmax(rmse_values)]['missing_rate']:.1f})")
        print(f"平均插补时间: {np.mean(time_values):.2f} 秒")
        print(f"时间标准差: {np.std(time_values):.2f} 秒")
    
    # 保存结果到CSV
    # results_df = pd.DataFrame(results)
    # results_df.to_csv('fancyimpute_softimpute_results.csv', index=False)
    # print(f"\n结果已保存到 'fancyimpute_softimpute_results.csv'")

    # 输出保存的插补数据文件总结
    print(f"\n📁 插补数据文件保存总结:")
    print("-" * 50)
    valid_results = [r for r in results if 'error' not in r and 'output_file' in r]
    for result in valid_results:
        print(f"✓ 缺失率 {result['missing_rate']:.1f}: {result['output_file']}")
    print(f"\n总共保存了 {len(valid_results)} 个插补数据文件")
    
    # 输出方法特点
    if FANCYIMPUTE_AVAILABLE:
        print(f"\nFancyImpute SoftImpute插补方法特点:")
        print("-" * 30)
        print("✓ 基于矩阵分解的低秩近似方法")
        print("✓ 适用于大规模矩阵补全问题")
        print("✓ 使用奇异值分解(SVD)进行矩阵分解")
        print("✓ 自动选择收缩参数和矩阵秩")
        print("✓ 支持收敛阈值控制精度")
        print("✓ 适合处理连续型数据")
        print("✗ 对于小数据集可能过拟合")
        print("✗ 计算复杂度较高")
    else:
        print(f"\nSoftImpute插补方法特点 (替代实现):")
        print("-" * 30)
        print("⚠ 使用简单均值填充替代实现")
        print("⚠ 仅用于测试代码结构")
        print("✓ 快速执行，低内存占用")
        print("✗ 插补效果有限")
        print("✗ 不具备矩阵分解能力")

    print(f"✓ 最大迭代次数: {imputer.max_iters}")
    print(f"✓ 收敛阈值: {imputer.convergence_threshold}")
    # fancyimpute的SoftImpute可能没有init_fill_method属性
    try:
        print(f"✓ 初始填充方法: {imputer.init_fill_method}")
    except AttributeError:
        print("✓ 初始填充方法: 默认方法")
    
    return results

if __name__ == "__main__":
    results = main()
