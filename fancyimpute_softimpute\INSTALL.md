# 安装和运行指南

## 依赖安装

### 1. 安装 fancyimpute
```bash
# 方法1: 使用 pip 安装
pip install fancyimpute

# 方法2: 如果上述方法失败，尝试从源码安装
pip install git+https://github.com/iskandr/fancyimpute.git

# 方法3: 使用 conda 安装
conda install -c conda-forge fancyimpute
```

### 2. 安装其他依赖
```bash
pip install pandas numpy scikit-learn
```

### 3. 可能需要的额外依赖
```bash
# 如果遇到 TensorFlow 相关错误
pip install tensorflow

# 如果遇到 CVX 相关错误
pip install cvxpy

# 如果遇到编译错误，可能需要安装编译工具
# Windows: 安装 Visual Studio Build Tools
# Linux: sudo apt-get install build-essential
# macOS: xcode-select --install
```

## 常见安装问题

### 问题1: fancyimpute 安装失败
**解决方案:**
```bash
# 尝试使用较旧版本的依赖
pip install numpy==1.19.5 scipy==1.7.3
pip install fancyimpute
```

### 问题2: TensorFlow 版本冲突
**解决方案:**
```bash
# 安装兼容版本的 TensorFlow
pip install tensorflow==2.8.0
```

### 问题3: 编译错误
**解决方案:**
```bash
# 确保有 C++ 编译器
# Windows: 安装 Microsoft C++ Build Tools
# Linux: sudo apt-get install gcc g++
# macOS: xcode-select --install
```

## 运行步骤

### 1. 检查数据文件
确保 `data/breast.csv` 文件存在：
```bash
ls ../data/breast.csv
```

### 2. 运行插补程序
```bash
cd fancyimpute_softimpute
python softimpute_fancyimpute.py
```

### 3. 查看结果
程序运行完成后，会生成：
- `fancyimpute_softimpute_results.csv`: 详细结果数据
- 控制台输出: 实时进度和统计信息

## 测试安装

运行测试脚本验证安装：
```bash
python test_structure.py
```

## 替代方案

如果 fancyimpute 安装困难，可以考虑：

### 1. 使用 softimpute 包
```bash
pip install softimpute
```
然后修改导入语句：
```python
from softimpute import SoftImpute
```

### 2. 使用 sklearn 的替代方法
```python
from sklearn.impute import IterativeImputer
from sklearn.decomposition import TruncatedSVD
```

### 3. 手动实现 SoftImpute
参考现有的 `softimpute` 文件夹中的实现。

## 性能优化建议

### 1. 内存优化
对于大数据集，考虑：
```python
# 减少最大迭代次数
max_iters=50

# 设置最大矩阵秩
max_rank=min(50, min(data.shape) // 2)
```

### 2. 速度优化
```python
# 放宽收敛条件
convergence_threshold=0.01

# 减少幂迭代次数
n_power_iterations=1
```

## 故障排除

### 1. 内存不足
- 减少数据集大小
- 设置较小的 max_rank
- 使用数据分块处理

### 2. 收敛问题
- 增加 max_iters
- 调整 convergence_threshold
- 尝试不同的 init_fill_method

### 3. 精度问题
- 检查数据预处理
- 调整 shrinkage_value
- 验证数据质量

## 联系支持

如果遇到问题，可以：
1. 查看 fancyimpute 官方文档
2. 检查 GitHub issues
3. 尝试使用替代实现方案
