#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试KNN插补保存功能
使用小数据集快速验证
"""

import pandas as pd
import numpy as np
import time
import warnings
from sklearn.impute import KNNImputer
warnings.filterwarnings('ignore')

# 导入主程序的函数
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from knn_imputation import normalization, rmse_loss, create_missing_data

def test_knn_save_feature():
    """测试KNN插补保存功能"""
    print("=" * 60)
    print("测试KNN插补数据保存功能")
    print("=" * 60)
    
    # 创建小测试数据集
    np.random.seed(42)
    test_data = pd.DataFrame(
        np.random.randn(50, 4) * 10 + 50,  # 50行4列
        columns=[f'feature_{i}' for i in range(4)]
    )
    
    print(f"测试数据集: {test_data.shape}")
    print(f"数据范围: [{test_data.min().min():.2f}, {test_data.max().max():.2f}]")
    
    # 模拟数据集名称
    dataset_name = "Test"
    
    # 测试缺失率
    missing_rates = [0.1, 0.2]  # 只测试两个缺失率
    results = []
    
    for missing_rate in missing_rates:
        print(f"\n处理缺失率: {missing_rate}")
        print("-" * 30)
        
        # 创建缺失数据
        data_missing = create_missing_data(test_data, missing_rate)
        missing_mask = data_missing.isna()
        
        print(f"缺失值数量: {missing_mask.sum().sum()}")
        
        # 初始化KNN插补器
        imputer = KNNImputer(n_neighbors=3, weights='uniform')
        
        # 执行插补
        start_time = time.time()
        data_imputed = imputer.fit_transform(data_missing)
        data_imputed = pd.DataFrame(data_imputed, columns=test_data.columns)
        end_time = time.time()
        
        # 计算RMSE
        data_m = (~missing_mask).astype(int).values
        rmse = rmse_loss(test_data.values, data_imputed.values, data_m)
        
        # 保存插补数据（模拟主程序的保存逻辑）
        output_filename = f"KNN-{dataset_name}-{missing_rate:.1f}.csv"
        data_imputed.to_csv(output_filename, index=False)
        
        print(f"✓ 插补完成，RMSE: {rmse:.4f}")
        print(f"✓ 数据已保存: {output_filename}")
        
        # 验证保存的文件
        try:
            saved_data = pd.read_csv(output_filename)
            print(f"✓ 文件验证成功，形状: {saved_data.shape}")
            print(f"✓ 保存数据中的缺失值: {saved_data.isna().sum().sum()}")
            
            # 验证数据完整性
            if saved_data.shape == test_data.shape:
                print("✓ 数据形状匹配")
            else:
                print("❌ 数据形状不匹配")
                
        except Exception as e:
            print(f"❌ 文件验证失败: {e}")
        
        results.append({
            'missing_rate': missing_rate,
            'rmse': rmse,
            'output_file': output_filename,
            'file_size_kb': round(os.path.getsize(output_filename) / 1024, 2) if os.path.exists(output_filename) else 0
        })
    
    # 输出总结
    print(f"\n" + "=" * 60)
    print("KNN插补保存功能测试结果")
    print("=" * 60)
    print(f"{'缺失率':<10} {'RMSE':<10} {'文件名':<20} {'大小(KB)':<10}")
    print("-" * 50)
    
    for result in results:
        print(f"{result['missing_rate']:<10.1f} {result['rmse']:<10.4f} {result['output_file']:<20} {result['file_size_kb']:<10.2f}")
    
    print(f"\n🎉 KNN插补保存功能测试完成！")
    print(f"📁 共保存了 {len(results)} 个插补数据文件")
    
    # 验证数据流程
    print(f"\n" + "=" * 60)
    print("数据流程验证")
    print("=" * 60)
    print("1. ✓ 原始数据 → 创建缺失值")
    print("2. ✓ 缺失数据 → KNN插补")
    print("3. ✓ 插补数据 → 保存CSV文件")
    print("4. ✓ 文件命名: KNN-数据集名称-缺失率.csv")
    print("5. ✓ 保存的是完整插补后的数据")
    
    # 清理测试文件（可选）
    cleanup = input("\n是否删除测试文件？(y/n): ").lower().strip()
    if cleanup == 'y':
        for result in results:
            try:
                os.remove(result['output_file'])
                print(f"✓ 已删除: {result['output_file']}")
            except:
                print(f"⚠ 删除失败: {result['output_file']}")

if __name__ == "__main__":
    test_knn_save_feature()
