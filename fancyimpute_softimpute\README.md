# FancyImpute SoftImpute 插补方法

## 概述

本文件夹包含使用 `fancyimpute` 包中的 `SoftImpute` 方法进行数据插补的实现。SoftImpute 是一种基于矩阵分解的低秩近似方法，特别适用于大规模矩阵补全问题。

## 方法特点

### ✅ 优势
- **矩阵分解方法**: 基于奇异值分解(SVD)的低秩近似
- **自适应参数**: 自动选择收缩参数和矩阵秩
- **高精度**: 支持收敛阈值控制，确保插补精度
- **适用性广**: 特别适合处理连续型数据和大规模数据集
- **理论基础**: 有坚实的数学理论支撑

### ❌ 局限性
- **计算复杂度**: 对于大矩阵计算开销较大
- **过拟合风险**: 在小数据集上可能出现过拟合
- **内存需求**: 需要较多内存存储中间矩阵
- **参数敏感**: 对初始化和参数设置较为敏感

## 算法原理

SoftImpute 通过以下步骤进行矩阵插补：

1. **初始化**: 使用指定方法(如零填充)初始化缺失值
2. **SVD分解**: 对当前矩阵进行奇异值分解
3. **软阈值**: 对奇异值应用软阈值操作
4. **重构**: 使用处理后的奇异值重构矩阵
5. **迭代**: 重复步骤2-4直到收敛

## 参数配置

```python
SoftImpute(
    shrinkage_value=None,      # 自动选择收缩参数
    convergence_threshold=0.001,  # 收敛阈值
    max_iters=100,             # 最大迭代次数
    max_rank=None,             # 最大矩阵秩，None表示自动选择
    n_power_iterations=1,      # 幂迭代次数
    init_fill_method="zero",   # 初始填充方法
    min_value=None,            # 最小值约束
    max_value=None,            # 最大值约束
    normalizer=None,           # 标准化方法
    verbose=False              # 不显示详细输出
)
```

## 使用方法

### 安装依赖
```bash
pip install fancyimpute
pip install pandas numpy
```

### 运行插补
```bash
cd fancyimpute_softimpute
python softimpute_fancyimpute.py
```

## 数据集

本实现使用 `breast.csv` 数据集进行测试，这是一个乳腺癌数据集，包含多个连续型特征。

## 评估指标

- **RMSE**: 标准化均方根误差
- **插补时间**: 完成插补所需的时间
- **缺失值数量**: 人工创建的缺失值统计

## 实验设置

- **缺失率**: [0.1, 0.2, 0.3, 0.4, 0.5]
- **随机种子**: 42 (确保可重复性)
- **评估方式**: 在标准化数据上计算RMSE

## 输出文件

### 实验结果文件
- `fancyimpute_softimpute_results.csv`: 详细的实验结果统计

### 插补数据文件
程序会为每个缺失率保存完整的插补数据，文件命名格式：
- `SoftImpute-数据集名称-缺失率.csv`

例如，使用News数据集时会生成：
- `SoftImpute-News-0.1.csv` (10%缺失率的插补数据)
- `SoftImpute-News-0.2.csv` (20%缺失率的插补数据)
- `SoftImpute-News-0.3.csv` (30%缺失率的插补数据)
- `SoftImpute-News-0.4.csv` (40%缺失率的插补数据)
- `SoftImpute-News-0.5.csv` (50%缺失率的插补数据)

### 控制台输出
- 实时的插补进度和性能统计
- 文件保存确认信息

## 与其他方法的比较

| 方法 | 基础原理 | 适用场景 | 计算复杂度 |
|------|----------|----------|------------|
| SoftImpute | 矩阵分解 | 大规模连续数据 | 高 |
| KNN | 相似性 | 中小规模混合数据 | 中 |
| MICE | 迭代回归 | 复杂依赖关系 | 高 |
| MissForest | 随机森林 | 非线性关系 | 中 |

## 注意事项

1. **内存使用**: 大数据集可能需要大量内存
2. **收敛性**: 某些情况下可能需要调整收敛阈值
3. **数据预处理**: 建议对数据进行适当的标准化
4. **参数调优**: 可根据具体数据集调整参数以获得更好效果
